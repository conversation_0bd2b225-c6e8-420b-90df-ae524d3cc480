import json
from typing import Dict, List, Optional, Any, Set, Tuple
import re
from collections import defaultdict
import numpy as np
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity
import pickle
import os
from pathlib import Path
import torch

class HTSIndices:
    """Class to store and manage HTS indices"""
    def __init__(self):
        self.code_index: Dict = {}
        self.word_index: defaultdict = defaultdict(set)
        self.description_embeddings: Dict = {}
        self.raw_data: List[dict] = []

    def save(self, directory: str):
        """Save indices to disk"""
        os.makedirs(directory, exist_ok=True)
        
        # Save raw data
        with open(os.path.join(directory, 'raw_data.json'), 'w') as f:
            json.dump(self.raw_data, f)
            
        # Save indices
        indices = {
            'code_index': self.code_index,
            'word_index': dict(self.word_index),  # Convert defaultdict to dict for serialization
            'description_embeddings': {
                k: v.cpu().numpy() for k, v in self.description_embeddings.items()
            }
        }
        with open(os.path.join(directory, 'indices.pkl'), 'wb') as f:
            pickle.dump(indices, f)

    @classmethod
    def load(cls, directory: str) -> 'HTSIndices':
        """Load indices from disk"""
        indices = cls()
        
        # Load raw data
        with open(os.path.join(directory, 'raw_data.json'), 'r') as f:
            indices.raw_data = json.load(f)
            
        # Load indices
        with open(os.path.join(directory, 'indices.pkl'), 'rb') as f:
            stored_indices = pickle.load(f)
            
        indices.code_index = stored_indices['code_index']
        indices.word_index = defaultdict(set, {
            k: set(v) for k, v in stored_indices['word_index'].items()
        })
        indices.description_embeddings = {
            k: torch.from_numpy(v) for k, v in stored_indices['description_embeddings'].items()
        }
        
        
        return indices

class HTSIndexBuilder:
    """Class responsible for building HTS indices"""
    def __init__(self, model_name: str = 'all-MiniLM-L6-v2'):
        self.model = SentenceTransformer(model_name)
    
    def build_indices(self, data: List[dict]) -> HTSIndices:
        """Build all indices from raw data"""
        indices = HTSIndices()
        indices.raw_data = data
        
        descriptions = []
        hts_codes = []
        
        for position, item in enumerate(data):
            if item.get('htsno'):
                hts_code = item['htsno']
                description = item.get('description', '')
                
                # Build code index
                indices.code_index[hts_code] = {
                    'position': position,
                    'description': description,
                    'general': item.get('general', ''),
                    'indent': item.get('indent', ''),
                    'units': item.get('units', [])
                }
                
                # Build word index
                if description:
                    cleaned_desc = re.sub(r'[^\w\s]', ' ', description.lower())
                    words = [w for w in cleaned_desc.split() if w]
                    
                    for word in words:
                        indices.word_index[word].add(hts_code)
                    
                    descriptions.append(description)
                    hts_codes.append(hts_code)
        
        # Generate embeddings
        if descriptions:
            embeddings = self.model.encode(descriptions, convert_to_tensor=True)
            for hts_code, embedding in zip(hts_codes, embeddings):
                indices.description_embeddings[hts_code] = embedding
                
        return indices

class HTSSemanticLookup:
    """Class for performing searches using pre-built indices"""
    def __init__(self, indices: HTSIndices, model_name: str = 'all-MiniLM-L6-v2'):
        self.indices = indices
        self.model = SentenceTransformer(model_name)
    
    def semantic_search(self, 
                       query: str, 
                       max_results: int = 10, 
                       min_similarity: float = 0.3) -> List[dict]:
        """Semantic search using pre-computed embeddings"""
        query_embedding = self.model.encode(query, convert_to_tensor=True)
        
        similarities = []
        for hts_code, embedding in self.indices.description_embeddings.items():
            similarity = cosine_similarity(
                query_embedding.cpu().numpy().reshape(1, -1),
                embedding.cpu().numpy().reshape(1, -1)
            )[0][0]
            
            if similarity >= min_similarity:
                similarities.append((hts_code, similarity))
        
        similarities.sort(key=lambda x: x[1], reverse=True)
        
        results = []
        for hts_code, similarity in similarities[:max_results]:
            result_temp = self.lookup_hts(hts_code)
            if result_temp:
                result_temp['hts_code'] = hts_code
                result_temp['similarity_score'] = float(similarity)
                results.append(result_temp)
        
        return results
    
    def hybrid_search(self, 
                     query: str, 
                     max_results: int = 10,
                     semantic_weight: float = 0.7) -> List[dict]:
        """Hybrid search combining semantic and keyword matching"""
        semantic_results = self.semantic_search(query, max_results=max_results * 2)
        keyword_results = self.search_by_description(query, max_results=max_results * 2)
        
        combined_scores = defaultdict(float)
        
        for result in semantic_results:
            combined_scores[result['hts_code']] += result['similarity_score'] * semantic_weight
            
        keyword_weight = 1 - semantic_weight
        for result in keyword_results:
            combined_scores[result['hts_code']] += (result['match_score'] / len(query.split())) * keyword_weight
        
        sorted_results = sorted(
            combined_scores.items(), 
            key=lambda x: x[1], 
            reverse=True
        )[:max_results]
        
        results = []
        for hts_code, score in sorted_results:
            result_temp = self.lookup_hts(hts_code)
            if result_temp:
                result_temp['hts_code'] = hts_code
                result_temp['combined_score'] = float(score)
                results.append(result_temp)
        
        return results
    
    def search_by_description(self, query: str, max_results: int = 10) -> List[dict]:
        """Keyword-based search using pre-computed word index"""
        cleaned_query = re.sub(r'[^\w\s]', ' ', query.lower())
        query_words = [w for w in cleaned_query.split() if w]
        
        matching_codes = defaultdict(int)
        for word in query_words:
            for hts_code in self.indices.word_index.get(word, set()):
                matching_codes[hts_code] += 1
        
        sorted_results = sorted(
            matching_codes.items(),
            key=lambda x: (-x[1], x[0])
        )[:max_results]
        
        results = []
        for hts_code, score in sorted_results:
            result_temp = self.lookup_hts(hts_code)
            if result_temp:
                result_temp['hts_code'] = hts_code
                result_temp['match_score'] = float(score)
                results.append(result_temp)
        
        return results

    def lookup_hts(self, hts_code: str) -> Optional[dict]:
        """Look up HTS code details using pre-computed code index"""
        # if hts_code not in self.indices.code_index:
        #     return None
        print("Total_Codes:",len(self.indices.code_index))
        print("Code of Interest:",hts_code)
        print(hts_code in self.indices.code_index)
        if hts_code not in self.indices.code_index:
            matched_codes = [code for code in self.indices.code_index if code.startswith(hts_code[:8])]
            if not matched_codes:
                return None
            if len(matched_codes)==1:
                hts_code = matched_codes[0] #incase only other is there
            else:
                hts_code = matched_codes[1]  # Use the first matched code
            
            
        item = self.indices.raw_data[self.indices.code_index[hts_code]['position']]
        #print(item)
        #exit(1)
        
        result = {
            'main_details': {
                'htsno': item.get('htsno', ''),
                'description': item.get('description', ''),
                'units': item.get('units', []),
                'general': item.get('general', ''),
                'special': item.get('special', ''),
                'other': item.get('other', ''),
                'indent': item.get('indent', ''),
                'superior': item.get('superior', '')
            },
            'footnote_references': []
        }
        
        if item.get('footnotes'):
            for footnote in item['footnotes']:
                if isinstance(footnote, dict) and 'columns' in footnote:
                    if 'general' in footnote['columns']:
                        footnote_value = footnote.get('value', '')
                        if 'See ' in footnote_value:
                            referenced_hts = footnote_value.split('See ')[1].strip('.')
                            #print(referenced_hts)
                            #referenced_hts = ".".join(referenced_hts.split(".")[:-1])
                            #print(referenced_hts)

                            if referenced_hts in self.indices.code_index:
                                ref_position = self.indices.code_index[referenced_hts]['position']
                                ref_item = self.indices.raw_data[ref_position]
                                result['footnote_references'].append({
                                    'referenced_code': referenced_hts,
                                    'footnote_value': footnote_value,
                                    'details': {
                                        'htsno': ref_item.get('htsno', ''),
                                        'description': ref_item.get('description', ''),
                                        'general': ref_item.get('general', ''),
                                        'special': ref_item.get('special', ''),
                                        'other': ref_item.get('other', '')
                                    }
                                })
                            else:
                                referenced_hts = ".".join(referenced_hts.split(".")[:-1])
                                #print(referenced_hts)                               
                                ref_position = self.indices.code_index[referenced_hts]['position']
                                ref_item = self.indices.raw_data[ref_position]
                                result['footnote_references'].append({
                                    'referenced_code': referenced_hts,
                                    'footnote_value': footnote_value,
                                    'details': {
                                        'htsno': ref_item.get('htsno', ''),
                                        'description': ref_item.get('description', ''),
                                        'general': ref_item.get('general', ''),
                                        'special': ref_item.get('special', ''),
                                        'other': ref_item.get('other', '')
                                    }
                                })                                

        
        return result

# Example usage:
if __name__ == "__main__":
    # Path for storing indices
    INDEX_DIR = "hts_indices"
    
    # Check if indices exist
    if not os.path.exists(INDEX_DIR):
        print("Building indices for the first time...")
        
        # Load raw data
        with open('htsdata_all.json', 'r') as file:
            hts_data = json.load(file)
        
        # Build indices
        builder = HTSIndexBuilder()
        indices = builder.build_indices(hts_data)
        
        # Save indices
        indices.save(INDEX_DIR)
        print("Indices built and saved successfully.")
    else:
        print("Loading pre-computed indices...")
        indices = HTSIndices.load(INDEX_DIR)
    
    # Create lookup system with loaded indices
    lookup = HTSSemanticLookup(indices)
    
    # Example searches
    #semantic_results = lookup.semantic_search("epoxy resins")
    #semantic_results = lookup.semantic_search("Monoethanolamine") #MEA Triazine #Monoethanolamine Triazines #Monoethanolamine

    results = lookup.lookup_hts("2921.19.11.00")#"2933.61.00.00") #("2922.15.00.00")
    print(results)
    #hybrid_results = lookup.hybrid_search("epoxy resins")
    #top_k_results = 2
    #print("\nSemantic search results:", json.dumps(semantic_results[:top_k_results], indent=2))
    #print("\nHybrid search results:", json.dumps(hybrid_results[:top_k_results], indent=2))