import pandas as pd
import sys
import os
import yaml
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
from src.tariff.extract_hts import *
#from src.volza.extract_hts_volza import *
from src.tariff.hts_duty_calculator import *
from src.tariff.hts_duty_calculator_allCountries import *

def standardize_code(code: str) -> str:
    # Remove all non-numeric characters
    numbers = ''.join(char for char in code if char.isdigit())
    
    # Pad with zeros if less than 10 digits
    if len(numbers) < 10:
        numbers = numbers.ljust(10, '0')
    elif len(numbers) > 10:
        raise ValueError("Input contains more than 10 digits")
    
    # Format into the desired pattern
    return f"{numbers[0:4]}.{numbers[4:6]}.{numbers[6:8]}.{numbers[8:10]}"

def calculate_duty_rate(product_name, country_code, hts_number):
    # Add config loading at the start of the function
    config_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'tariff_config.yaml')
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    print("Invoking Duty Calculator via 3 sources")
    duty={} #
    # Source 1: Tariff DB
    INDEX_DIR = "hts_indices_htsTariff"
    # Check if indices exist
    if not os.path.exists(INDEX_DIR):
        print("Building indices for the first time...")
        
        # Load raw data
        with open('htsdata_all.json', 'r') as file:
            hts_data = json.load(file)
        
        # Build indices
        builder = HTSIndexBuilder()
        indices = builder.build_indices(hts_data)
        
        # Save indices
        indices.save(INDEX_DIR)
        print("Indices built and saved successfully.")
    else:
        print("Loading pre-computed indices...")
        indices = HTSIndices.load(INDEX_DIR)

    # Create lookup system with loaded indices
    lookup = HTSSemanticLookup(indices)
    
    # Example searches
    semantic_results = lookup.semantic_search(product_name)
    print(semantic_results[0])
    if len(semantic_results) == 0:
        duty_semantics_Tariff="Null"
    #exit(1)

    tariff_df ={}
    tariff_semantic_similarity_score = semantic_results[0]['similarity_score']
    tariff_df['similarity_score'] = tariff_semantic_similarity_score
    tariff_df['htsno'] = semantic_results[0]['hts_code']
    tariff_df['hts_code_description'] = semantic_results[0]['main_details']['description']
    print(tariff_semantic_similarity_score)
    #exit(1)
    if (tariff_semantic_similarity_score>=0.50):
        top_k_results = 1
        duty_semantics_Tariff = parse_hts_data_all_countries(semantic_results[:top_k_results])
        tariff_df['tariff_info'] = duty_semantics_Tariff  # This will now contain footer_duty instead of anti_dumping_duty
    else:
        tariff_df['tariff_info'] = "Null"
        
    duty["TariffDB"]=tariff_df
    print("************Tariff*******")
    #print(duty_semantics_Tariff)
    #exit(1)
    #source 2:
    hts_code = standardize_code(hts_number)
    print(hts_code)
    perplexity_results = lookup.lookup_hts(hts_code)
    if perplexity_results==None:
        return "Sorry, I am not able to retrieve the tariff names. Try providing an accurate product name or HS Code. Thanks "

    #print(perplexity_results)
    perplexity_results_list=[perplexity_results]
    duty_perplexity = parse_hts_data_all_countries(perplexity_results_list)    

    perplexity_df ={}
    perplexity_df['hts_code'] = semantic_results[0]['hts_code']    
    perplexity_df["tariif_info"]=duty_perplexity
    duty["Perplexity"]= perplexity_df
    print("************Perplexity*******")
    #print(duty_perplexity)
    #exit(1)

    VOLZA_INDEX_DIR = "hts_indices_volza"
    
    # Check if indices exist
    if not os.path.exists(VOLZA_INDEX_DIR):
        print("Building indices for the first time...")

        json_ready =1

        if json_ready == 0:

            df = pd.read_parquet("sample_volza_trade_data_summary_df_semantics_13022025.parquet")
            # Convert to JSON
            df.to_json("volza_trade_data_semantic_summary_13022025.json", orient="records", indent=4)
            print("Conversion complete! JSON file saved as 'output.json'") 


            import json
            from collections import defaultdict

            # Load the JSON file
            with open("volza_trade_data_semantic_summary_13022025.json", "r", encoding="utf-8") as f:
                data = json.load(f)

            # Dictionary to store grouped results
            grouped_data = defaultdict(lambda: {"PRODUCT_DETAILS": [], "TRANSACTION_COUNT": 0})

            # Process each entry
            for item in data:
                key = (item["HS_Code"], item["HS_CODE_DESCRIPTOR"])
                
                # Append product details and update transaction count
                grouped_data[key]["PRODUCT_DETAILS"].append(item["PRODUCT_DETAILS"])
                grouped_data[key]["TRANSACTION_COUNT"] += item["TRANSACTION_COUNT"]

            # Convert dictionary back to list format
            final_data = [
                {
                    "HS_Code": hs_code,
                    "HS_CODE_DESCRIPTOR": descriptor,
                    "PRODUCT_DETAILS": values["PRODUCT_DETAILS"],
                    "TRANSACTION_COUNT": values["TRANSACTION_COUNT"]
                }
                for (hs_code, descriptor), values in grouped_data.items()
            ]

            # Save to a new JSON file
            with open("volza_trade_data_semantic_summary_13022025_final.json", "w", encoding="utf-8") as f:
                json.dump(final_data, f, indent=4, ensure_ascii=False)

                print("✅ JSON transformation complete! Saved as 'output.json'")


        with open('volza_trade_data_semantic_summary_13022025_final.json', 'r') as file:
            hts_data = json.load(file)   

        #exit(1)
        # Build indices
        builder = Volza_HTSIndexBuilder()
        volza_indices = builder.build_indices(hts_data)
        
        # Save indices
        indices.save(VOLZA_INDEX_DIR)
        print("Indices built and saved successfully.")
    else:
        print("Loading pre-computed indices...")
        volza_indices = Volza_HTSIndices.load(VOLZA_INDEX_DIR)
    
    # Create lookup system with loaded indices
    volza_lookup = Volza_HTSSemanticLookup(volza_indices)
    
    # Example searches
    #semantic_results = lookup.semantic_search("epoxy resins")
    volza_semantic_results = volza_lookup.semantic_search(product_name) #MEA Triazine #Monoethanolamine Triazines #Monoethanolamine
    print(volza_semantic_results[0])
    if len(volza_semantic_results) == 0:
        duty_semantics_volza="Null"
    #exit(1)
    volza_df ={}
    volza_semantic_similarity_score = volza_semantic_results[0]['similarity_score']
    volza_df['similarity_score'] = volza_semantic_similarity_score
    volza_df['hs_code'] = volza_semantic_results[0]['hs_code']
    volza_df['hs_code_description'] = volza_semantic_results[0]['main_details']['hs_code_description']
    volza_df['product_details'] = volza_semantic_results[0]['main_details']['product_details']
    print(volza_semantic_similarity_score)
    #exit(1)
    if (volza_semantic_similarity_score>=0.50):
        hts_code = standardize_code(hts_number)
        print(hts_code)
        volza_results = lookup.lookup_hts(hts_code)  
        if volza_results==None:
            volza_df['tariff_info']="Null"
        #print(perplexity_results)
        volza_results_list=[volza_results]
        duty_semantics_volza = parse_hts_data_all_countries(volza_results_list)    
        volza_df['tariff_info'] = duty_semantics_volza
    else:
         volza_df['tariff_info']="Null"
    
    duty["Volza"]=volza_df
    print("************Volza*******")
    #print(duty_semantics_Tariff)

    # Incremental_duty = 0
    # if country_code ==  "CN":
    #     Incremental_duty="Trumps recent announcement implies 10% additional duty on China"
    Incremental_duty = "NA"
    if country_code in config['incremental_duties']:
        Incremental_duty = config['incremental_duties'][country_code]['description']
   
    return {"standard duty":duty,"recent announcement of new_duty":Incremental_duty }

# Example usage:
if __name__=="__main__":

    product_name = "MEA Triazine" #"epoxy resins"
    country_code = 'CH'  # Australia

    duty_response = calculate_duty_rate(product_name, country_code,"29336100")
    print(duty_response)
